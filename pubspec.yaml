name: attp_2024
description: "NTSOFT ATTP là hệ thống thông tin quản lý an toàn thực phẩm sử dụng công nghệ điện toán đám mây để đồng bộ dữ liệu giữa các cơ quan quản lý, c<PERSON> sở sản xuất kinh doanh và người dân. <PERSON>ệ thống cung cấp các công cụ hỗ trợ quản lý thông tin cơ sở sản xuất, theo dõi việc cấp gi<PERSON>y chứng nh<PERSON>n, ký cam kết an toàn thực phẩm, gi<PERSON>m sát các vi phạm và cập nhật kết quả thanh kiểm tra. Đồng thời, NTSOFT ATTP còn thực hiện truyền thông kiến thức, c<PERSON><PERSON> báo nguy cơ an toàn thực phẩm và gửi thông bá<PERSON> kị<PERSON> thời, g<PERSON><PERSON> <PERSON>h<PERSON><PERSON> nâng cao hiệu quả quản lý, đ<PERSON><PERSON> b<PERSON>o an toàn thực phẩm và bảo vệ sức khỏe cộng đồng."
publish_to: "none"

# +5: Build
# version: 1.0.4
version: 1.0.11+0

environment:
  sdk: ">=3.3.4 <4.0.0"

flutter_launcher_icons:
  android: true
  ios: true
  image_path: "assets/logo/NTSOFTAppATTP_APPLE.png"
  adaptive_icon_background: "#ffffff"
  adaptive_icon_foreground: "assets/logo/NTSOFTAppATTP_APPLE.png"

# package_rename_config:
#   android:
#     app_name: "NTSOFT ATTP" # Tên ứng dụng Android
#     package_name: "com.nts.attp.haugiang"# Package name Android
#     override_old_package: "true" # Xóa thư mục cũ

#   ios:
#     app_name: "NTSOFT ATTP" # Tên ứng dụng iOS
#     bundle_name: "haugiang" # Tên ngắn gọn (<= 15 ký tự)
#     package_name: "com.nts.attp.haugiang" # Bundle Identifier iOS

dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.6
  # getwidget: ^2.0.4
  google_maps_flutter: ^2.10.0
  location: ^8.0.1
  flutter_polyline_points: ^3.0.1
  geolocator: ^14.0.2
  curved_navigation_bar: ^1.0.3
  device_info_plus: ^11.5.0
  syncfusion_flutter_charts: ^30.1.42
  d_chart: ^3.0.0
  http: ^1.2.1
  uuid: ^4.5.1
  flutter_svg: ^2.0.17
  fluttertoast: ^8.2.8
  local_auth: ^2.1.7
  google_places_flutter: ^2.0.8
  get: ^4.6.6
  google_fonts: ^6.2.1
  lottie: ^3.3.1
  loading_animation_widget: ^1.2.1
  intl: ^0.20.2
  percent_indicator: ^4.2.3
  syncfusion_flutter_gauges: ^30.1.42
  flutter_staggered_grid_view: ^0.7.0
  shared_preferences: ^2.2.2
  table_calendar: ^3.1.1
  cached_network_image: ^3.4.1
  shimmer: ^3.0.0
  firebase_storage: ^13.0.0
  image_picker: ^1.0.8
  dropdown_button2: ^2.3.9
  get_it: ^8.0.0

  permission_handler: ^12.0.1
  flutter_form_builder: ^10.1.0
  infinite_scroll_pagination: ^5.1.0
  dio: ^5.7.0
  flutter_dotenv: ^5.1.0
  fl_chart: ^0.68.0

  flutter_localization: ^0.3.3
  responsive_sizer: ^3.3.1
  dropdown_search: ^6.0.1
  geocoding: ^4.0.0
  signature: ^6.3.0
  font_awesome_flutter: ^10.7.0
  path_provider: ^2.1.4
  mime: ^2.0.0
  http_parser: ^4.0.2
  gap: ^3.0.1
  faker: ^2.2.0
  flutter_widget_from_html: ^0.17.0
  logger: ^2.5.0
  share_plus: ^11.0.0
  html: ^0.15.5
  app_links: ^6.3.3
  pdf: ^3.11.1
  url_launcher: ^6.3.1
  carousel_slider: ^5.0.0
  file_picker: ^10.2.0
  dart_ipify: ^1.1.1
  json_annotation: ^4.9.0
  scrollable_list_tab_scroller: ^3.1.0
  flutter_launcher_icons: ^0.14.2
  syncfusion_flutter_datagrid: ^30.1.42
  carousel_slider_plus: ^7.1.0
  video_player: ^2.9.2
  webview_flutter: ^4.10.0
  flutter_pdfview: ^1.4.0
  package_rename: ^1.8.0
  change_app_package_name: ^1.4.0
  flutter_screenutil: ^5.9.3
  device_preview: ^1.2.0
  getwidget: ^6.0.0
  collection: ^1.19.0
  dart_levenshtein: ^1.0.1
  fuzzywuzzy: ^1.2.0
  diacritic: ^0.1.6
  flutter_chat_ui: ^2.9.0
  open_filex: ^4.6.0
  speech_to_text: ^7.0.0
  photo_manager: ^3.6.4
  wechat_assets_picker: ^9.4.2
  video_compress: ^3.1.4
  flutter_markdown: ^0.7.6+2
  rename: ^3.0.2

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^6.0.0
  json_serializable: ^6.9.2
  build_runner: ^2.4.14

flutter:
  uses-material-design: true
  
  assets:
    - assets/images/
    - assets/icons/
    - assets/fonts/
    - .env
    - lottie/
    - lotties/
    - lib/generated/assets.dart

  fonts:
    - family: SF Pro
      fonts:
        - asset: assets/fonts/SF-Pro-Display-Regular.otf
          weight: 400
        - asset: assets/fonts/SF-Pro-Display-Medium.otf
          weight: 500
        - asset: assets/fonts/SF-Pro-Display-Bold.otf
          weight: 700
        - asset: assets/fonts/SF-Pro-Display-Light.otf
          weight: 300
          style: italic
